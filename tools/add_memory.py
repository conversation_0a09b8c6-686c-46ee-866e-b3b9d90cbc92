from collections.abc import Generator
from typing import Any, Dict, List
import json
import httpx
import time
from dify_plugin import Tool
from dify_plugin.entities.tool import ToolInvokeMessage
from utils.logger import (
    get_tool_logger, 
    log_request_info, 
    log_content_preview, 
    log_api_response, 
    log_error, 
    log_operation_result
)

class Mem0Tool(Tool):
    def _invoke(self, tool_parameters: dict[str, Any]) -> Generator[ToolInvokeMessage, None, None]:
        # 初始化日志记录器
        logger = get_tool_logger("add_memory")
        
        # Get API key from credentials
        api_key = self.runtime.credentials["mem0_api_key"]
        
        # Get API URL from credentials or use default (consistent with provider/mem0.yaml)
        api_url = self.runtime.credentials.get("mem0_api_url", "http://localhost:8000")
        if not api_url or api_url.strip() == "":
            api_url = "http://localhost:8000"
        api_url = api_url.rstrip("/")
        
        # 记录请求基本信息
        log_request_info(logger, "AddMemory", tool_parameters, api_url)
        
        # Format messages
        user_content = tool_parameters["user"]
        assistant_content = tool_parameters["assistant"]
        messages = [
            {"role": "user", "content": user_content},
            {"role": "assistant", "content": assistant_content}
        ]
        
        # 记录内容预览
        log_content_preview(logger, "AddMemory", user_content, "用户消息", 50)
        log_content_preview(logger, "AddMemory", assistant_content, "助手回复", 50)
        
        # Prepare payload
        payload = {
            "messages": messages,
            "user_id": tool_parameters["user_id"]
        }

        # Add optional parameters
        if tool_parameters.get("agent_id"):
            payload["agent_id"] = tool_parameters["agent_id"]

        if tool_parameters.get("run_id"):
            payload["run_id"] = tool_parameters["run_id"]

        # Handle inference parameter (default to True)
        infer = tool_parameters.get("infer", True)
        payload["infer"] = infer

        # Handle metadata
        if tool_parameters.get("metadata"):
            try:
                metadata = json.loads(tool_parameters["metadata"])
                payload["metadata"] = metadata
                logger.debug(f"[AddMemory] 元数据已解析: {len(metadata)} 个字段")
            except json.JSONDecodeError as e:
                error_message = f"Invalid JSON in metadata: {str(e)}"
                log_error(logger, "AddMemory", e, "解析元数据JSON时")
                yield self.create_json_message({
                    "status": "error",
                    "error": error_message
                })
                yield self.create_text_message(f"Failed to add memory: {error_message}")
                return

        # Custom instructions support
        if tool_parameters.get("custom_instructions"):
            payload["custom_instructions"] = tool_parameters["custom_instructions"]

        if tool_parameters.get("custom_categories"):
            try:
                if isinstance(tool_parameters["custom_categories"], str):
                    payload["custom_categories"] = json.loads(tool_parameters["custom_categories"])
                else:
                    payload["custom_categories"] = tool_parameters["custom_categories"]
                logger.debug(f"[AddMemory] 自定义分类已设置: {len(payload['custom_categories'])} 个分类")
            except json.JSONDecodeError as e:
                error_message = f"Invalid JSON in custom_categories: {str(e)}"
                log_error(logger, "AddMemory", e, "解析自定义分类JSON时")
                yield self.create_json_message({
                    "status": "error",
                    "error": error_message
                })
                yield self.create_text_message(f"Failed to add memory: {error_message}")
                return

        # Async client configuration
        use_async = tool_parameters.get("use_async_client", False)
        if use_async:
            payload["async_processing"] = True

        # Selective memory parameters
        if tool_parameters.get("memory_priority"):
            payload["priority"] = tool_parameters["memory_priority"]

        if tool_parameters.get("auto_prune"):
            payload["auto_prune"] = tool_parameters["auto_prune"]
        
        # 计时开始
        start_time = time.time()
        
        # Make direct HTTP request to mem0 API
        try:
            # 发送请求前记录
            logger.info(f"[AddMemory] 发送API请求: POST {api_url}/v1/memories/")
            
            response = httpx.post(
                f"{api_url}/v1/memories/",
                json=payload,
                headers={"Authorization": f"Token {api_key}"},
                timeout=30
            )
            
            # 计算请求耗时并记录响应信息
            request_time = time.time() - start_time
            log_api_response(logger, "AddMemory", response, request_time)
            
            # 检查响应状态
            response.raise_for_status()
            
            # 解析响应
            result = response.json()
            logger.debug(f"[AddMemory] 原始响应数据: {json.dumps(result, ensure_ascii=False)[:200]}...")
            
            # Handle different response formats
            memory_ids = []
            
            # 处理不同格式的响应
            if isinstance(result, dict):
                if "memory_id" in result:
                    # 标准格式: {"memory_id": "xxx"}
                    memory_ids.append(result["memory_id"])
                    logger.info(f"[AddMemory] 标准格式响应，内存ID: {result['memory_id']}")
                elif "id" in result:
                    # 备用格式: {"id": "xxx"}
                    memory_ids.append(result["id"])
                    logger.info(f"[AddMemory] 备用格式响应，内存ID: {result['id']}")
                elif "results" in result and isinstance(result["results"], list):
                    # 结果集合格式: {"results": [{...}, ...]}
                    for r in result["results"]:
                        if isinstance(r, dict):
                            if "event" in r and r["event"] == "ADD" and "id" in r:
                                memory_ids.append(r["id"])
                            elif "id" in r:
                                memory_ids.append(r["id"])
                    logger.info(f"[AddMemory] 结果集合格式，找到 {len(memory_ids)} 个内存ID")
            elif isinstance(result, list):
                # 直接列表格式: [{...}, ...]
                for r in result:
                    if isinstance(r, dict):
                        if "event" in r and r["event"] == "ADD" and "id" in r:
                            memory_ids.append(r["id"])
                        elif "id" in r:
                            memory_ids.append(r["id"])
                logger.info(f"[AddMemory] 列表格式，找到 {len(memory_ids)} 个内存ID")
            
            # 记录操作结果
            result_data = {"memory_ids": memory_ids}
            log_operation_result(logger, "AddMemory", True, result_data)
                
            # 计算总耗时
            total_time = time.time() - start_time
            logger.info(f"[AddMemory] 总处理时间: {total_time:.3f}秒")
            
            # Return JSON format
            yield self.create_json_message({
                "status": "success",
                "messages": messages,
                "memory_ids": memory_ids
            })
            
            # Return text format
            text_response = "Memory added successfully\n\n"
            text_response += "Added messages:\n"
            for msg in messages:
                text_response += f"- {msg['role']}: {msg['content']}\n"
            
            if memory_ids:
                text_response += f"\nMemory IDs: {', '.join(memory_ids)}"
            else:
                text_response += "\nNo memory IDs returned. Memory may still have been added."
            
            yield self.create_text_message(text_response)
            
        except httpx.HTTPStatusError as e:
            error_message = f"HTTP error: {e.response.status_code}"
            try:
                error_data = e.response.json()
                if "detail" in error_data:
                    error_message = f"Error: {error_data['detail']}"
            except:
                pass
            
            log_error(logger, "AddMemory", e, f"HTTP状态错误 {e.response.status_code}")
            
            yield self.create_json_message({
                "status": "error",
                "error": error_message
            })
            
            yield self.create_text_message(f"Failed to add memory: {error_message}")
            
        except Exception as e:
            log_error(logger, "AddMemory", e, "处理请求时发生异常")
            
            yield self.create_json_message({
                "status": "error",
                "error": str(e)
            })
            
            yield self.create_text_message(f"Failed to add memory: {str(e)}")
